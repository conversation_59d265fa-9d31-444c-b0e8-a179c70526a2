{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"stream\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 16394819259391542128, "path": 9451129688232112278, "deps": [[40386456601120721, "percent_encoding", false, 5645422985644960166], [442316063022428917, "rustls_pemfile", false, 9535756512885775374], [1133100163585637996, "tower_service", false, 8542190278749531387], [2506748060575166999, "rustls_pki_types", false, 13655805748167027646], [2809744263162057416, "webpki_roots", false, 17751290671001318503], [3150220818285335163, "url", false, 15646534335131700399], [3722963349756955755, "once_cell", false, 837241060338100391], [4800206021143169329, "pin_project_lite", false, 18376117162643747689], [5379354381544936779, "log", false, 1232695720869729984], [8569119365930580996, "serde_json", false, 11957948696869518525], [8739336841983680076, "http_body", false, 5614151850130134300], [8858041990736880586, "tokio", false, 8683366689374827085], [9233747861018032993, "winreg", false, 14568709748746982315], [9689903380558560274, "serde", false, 14409978779600907386], [10113933405492342445, "rustls", false, 16920940981164559498], [10229185211513642314, "mime", false, 15818505040018847177], [10785045061002481594, "hyper_util", false, 974824801450201522], [11913130400938634928, "futures_util", false, 9444540597871725481], [12588177665552295757, "futures_core", false, 12543737256471076264], [13077212702700853852, "base64", false, 1490068513138689605], [13410946177877314675, "sync_wrapper", false, 4885630705344407889], [14193253029976502517, "tokio_util", false, 13468966461019728889], [16227728351758841112, "bytes", false, 1330343931943844836], [16253538096806714255, "ipnet", false, 16859874725663231274], [16333393004512305118, "hyper", false, 10213853246638115499], [16428869156911197497, "http_body_util", false, 9794688600509816024], [16542808166767769916, "serde_urlencoded", false, 17469510082835335079], [16683675453290495500, "hyper_rustls", false, 6531280885069229089], [17301905655554462353, "tokio_rustls", false, 13867960823537583375], [17860019243264344128, "http", false, 8684857293262761530]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-5656ad6b137d177a\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
import 'package:talker/talker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

// 全局变量
late Talker globalTalker;
late String globalDocumentPath;

// 初始化全局变量
Future<void> initGlobalVars() async {
  globalTalker = Talker();
  
  try {
    final directory = await getApplicationDocumentsDirectory();
    globalDocumentPath = directory.path;
    globalTalker.info('[Global] 文档路径: $globalDocumentPath');
  } catch (e) {
    globalTalker.error('[Global] 获取文档路径失败: $e');
    globalDocumentPath = '';
  }
}

import 'package:flutter/cupertino.dart';
import 'package:file_picker/file_picker.dart';
import 'package:app_rhyme/types/js_source_manager.dart';
import 'package:app_rhyme/types/js_music_source.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/colors.dart';
import 'package:chinese_font_library/chinese_font_library.dart';

class JsSourceManagementPage extends StatefulWidget {
  const JsSourceManagementPage({super.key});

  @override
  State<JsSourceManagementPage> createState() => _JsSourceManagementPageState();
}

class _JsSourceManagementPageState extends State<JsSourceManagementPage> {

  // 显示消息
  void _showMessage(String title, String message, {bool isError = false}) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(title, style: const TextStyle().useSystemChineseFont()),
        content: Text(message, style: const TextStyle().useSystemChineseFont()),
        actions: [
          CupertinoDialogAction(
            child: Text('确定', style: const TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );

    // 同时记录到日志
    if (isError) {
      globalTalker.error("$title: $message");
    } else {
      globalTalker.info("$title: $message");
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
    final backgroundColor = isDarkMode ? CupertinoColors.black : CupertinoColors.white;
    final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
    final iconColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;

    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        middle: Text(
          'JS音乐源管理',
          style: TextStyle(color: textColor).useSystemChineseFont(),
        ),
        leading: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () => Navigator.of(context).pop(),
          child: Icon(CupertinoIcons.back, color: iconColor),
        ),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () => _showAddSourceDialog(context, textColor, iconColor),
          child: Icon(CupertinoIcons.add, color: iconColor),
        ),
      ),
      child: SafeArea(
        child: _buildSourceList(context, textColor, iconColor),
      ),
    );
  }

  Widget _buildSourceList(BuildContext context, Color textColor, Color iconColor) {
    final sources = JsSourceManager.sources;
    final activeSourceId = JsSourceManager.activeSourceId;

    if (sources.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.music_note_list,
              size: 64,
              color: textColor.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无JS音乐源',
              style: TextStyle(
                color: textColor.withOpacity(0.7),
                fontSize: 18,
              ).useSystemChineseFont(),
            ),
            const SizedBox(height: 8),
            Text(
              '点击右上角的 + 按钮添加音乐源',
              style: TextStyle(
                color: textColor.withOpacity(0.5),
                fontSize: 14,
              ).useSystemChineseFont(),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sources.length,
      itemBuilder: (context, index) {
        final source = sources[index];
        final isActive = source.id == activeSourceId;

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: CupertinoColors.systemGrey6.resolveFrom(context),
            borderRadius: BorderRadius.circular(12),
            border: isActive
                ? Border.all(color: CupertinoColors.activeBlue, width: 2)
                : null,
          ),
          child: CupertinoListTile(
            padding: const EdgeInsets.all(16),
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isActive
                    ? CupertinoColors.activeBlue
                    : CupertinoColors.systemGrey4.resolveFrom(context),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                CupertinoIcons.music_note,
                color: isActive ? CupertinoColors.white : textColor,
                size: 20,
              ),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    source.name,
                    style: TextStyle(
                      color: textColor,
                      fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                    ).useSystemChineseFont(),
                  ),
                ),
                if (isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: CupertinoColors.activeBlue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '当前',
                      style: const TextStyle(
                        color: CupertinoColors.white,
                        fontSize: 12,
                      ).useSystemChineseFont(),
                    ),
                  ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  '作者: ${source.source.author}',
                  style: TextStyle(
                    color: textColor.withOpacity(0.7),
                    fontSize: 13,
                  ).useSystemChineseFont(),
                ),
                Text(
                  '版本: ${source.source.version}',
                  style: TextStyle(
                    color: textColor.withOpacity(0.7),
                    fontSize: 13,
                  ).useSystemChineseFont(),
                ),
                Text(
                  '添加时间: ${_formatDateTime(source.addedAt)}',
                  style: TextStyle(
                    color: textColor.withOpacity(0.5),
                    fontSize: 12,
                  ).useSystemChineseFont(),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!isActive)
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => _activateSource(source.id),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: CupertinoColors.activeBlue,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '启用',
                        style: const TextStyle(
                          color: CupertinoColors.white,
                          fontSize: 12,
                        ).useSystemChineseFont(),
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => _showSourceOptions(context, source, textColor),
                  child: Icon(
                    CupertinoIcons.ellipsis_vertical,
                    color: iconColor,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _activateSource(String sourceId) async {
    final success = await JsSourceManager.setActiveSource(sourceId);
    if (success) {
      setState(() {});
      _showMessage("切换成功", "已切换到新的音乐源");
    } else {
      _showMessage("切换失败", "无法切换到指定的音乐源", isError: true);
    }
  }

  void _showSourceOptions(BuildContext context, StoredJsSource source, Color textColor) {
    final isActive = source.id == JsSourceManager.activeSourceId;

    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(
          source.name,
          style: TextStyle().useSystemChineseFont(),
        ),
        message: Text(
          source.source.description,
          style: TextStyle().useSystemChineseFont(),
        ),
        actions: [
          if (!isActive)
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(context).pop();
                _activateSource(source.id);
              },
              child: Text('启用此源', style: TextStyle().useSystemChineseFont()),
            ),
          if (isActive)
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(context).pop();
                _deactivateSource();
              },
              child: Text('停用此源', style: TextStyle().useSystemChineseFont()),
            ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _showRenameDialog(context, source, textColor);
            },
            child: Text('重命名', style: TextStyle().useSystemChineseFont()),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _showDeleteConfirmation(context, source);
            },
            isDestructiveAction: true,
            child: Text('删除', style: TextStyle().useSystemChineseFont()),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('取消', style: TextStyle().useSystemChineseFont()),
        ),
      ),
    );
  }

  void _deactivateSource() async {
    final success = await JsSourceManager.setActiveSource(null);
    if (success) {
      setState(() {});
      _showMessage("停用成功", "已停用当前音乐源");
    } else {
      _showMessage("停用失败", "无法停用当前音乐源", isError: true);
    }
  }

  void _showRenameDialog(BuildContext context, StoredJsSource source, Color textColor) {
    String newName = source.name;

    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('重命名音乐源', style: TextStyle().useSystemChineseFont()),
        content: Column(
          children: [
            const SizedBox(height: 10),
            CupertinoTextField(
              controller: TextEditingController(text: source.name),
              placeholder: '请输入新名称',
              onChanged: (value) => newName = value,
              style: TextStyle().useSystemChineseFont(),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('取消', style: TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            child: Text('确定', style: TextStyle().useSystemChineseFont()),
            onPressed: () async {
              Navigator.of(context).pop();
              if (newName.trim().isNotEmpty && newName != source.name) {
                final success = await JsSourceManager.updateSource(source.id, name: newName.trim());
                if (success) {
                  setState(() {});
                  _showMessage("重命名成功", "音乐源已重命名");
                } else {
                  _showMessage("重命名失败", "无法重命名音乐源", isError: true);
                }
              }
            },
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, StoredJsSource source) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('删除音乐源', style: TextStyle().useSystemChineseFont()),
        content: Text(
          '确定要删除音乐源 "${source.name}" 吗？此操作无法撤销。',
          style: TextStyle().useSystemChineseFont(),
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('取消', style: TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: Text('删除', style: TextStyle().useSystemChineseFont()),
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await JsSourceManager.removeSource(source.id);
              if (success) {
                setState(() {});
                _showMessage("删除成功", "音乐源已删除");
              } else {
                _showMessage("删除失败", "无法删除音乐源", isError: true);
              }
            },
          ),
        ],
      ),
    );
  }

  void _showAddSourceDialog(BuildContext context, Color textColor, Color iconColor) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(
          '添加JS音乐源',
          style: TextStyle().useSystemChineseFont(),
        ),
        message: Text(
          '选择添加音乐源的方式',
          style: TextStyle().useSystemChineseFont(),
        ),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _showAddFromUrlDialog(context, textColor);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(CupertinoIcons.link, color: iconColor),
                const SizedBox(width: 8),
                Text('从URL导入', style: TextStyle().useSystemChineseFont()),
              ],
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _addFromFile();
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(CupertinoIcons.doc_text, color: iconColor),
                const SizedBox(width: 8),
                Text('从文件导入', style: TextStyle().useSystemChineseFont()),
              ],
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('取消', style: TextStyle().useSystemChineseFont()),
        ),
      ),
    );
  }

  void _showAddFromUrlDialog(BuildContext context, Color textColor) {
    String url = '';
    String customName = '';

    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('从URL导入JS源', style: TextStyle().useSystemChineseFont()),
        content: Column(
          children: [
            const SizedBox(height: 10),
            CupertinoTextField(
              placeholder: '请输入JS源的URL地址',
              onChanged: (value) => url = value,
              style: TextStyle().useSystemChineseFont(),
            ),
            const SizedBox(height: 10),
            CupertinoTextField(
              placeholder: '自定义名称（可选）',
              onChanged: (value) => customName = value,
              style: TextStyle().useSystemChineseFont(),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('取消', style: TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            child: Text('导入', style: TextStyle().useSystemChineseFont()),
            onPressed: () async {
              Navigator.of(context).pop();
              if (url.trim().isNotEmpty) {
                await _addFromUrl(url.trim(), customName.trim().isEmpty ? null : customName.trim());
              }
            },
          ),
        ],
      ),
    );
  }

  Future<void> _addFromUrl(String url, String? customName) async {
    try {
      final source = await JsMusicSource.fromUrl(url);
      final sourceId = await JsSourceManager.addSource(source, customName: customName);

      setState(() {});
      _showMessage("导入成功", "JS音乐源导入成功: ${source.name}");

      // 询问是否立即启用
      _askToActivateSource(sourceId, source.name);
    } catch (e) {
      _showMessage("导入失败", "JS音乐源导入失败: $e", isError: true);
    }
  }

  Future<void> _addFromFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['js', 'json'],
      );

      if (result != null) {
        final source = await JsMusicSource.fromFile(result.files.single.path!);
        final sourceId = await JsSourceManager.addSource(source);

        setState(() {});
        _showMessage("导入成功", "JS音乐源导入成功: ${source.name}");

        // 询问是否立即启用
        _askToActivateSource(sourceId, source.name);
      }
    } catch (e) {
      _showMessage("导入失败", "JS音乐源导入失败: $e", isError: true);
    }
  }

  void _askToActivateSource(String sourceId, String sourceName) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('启用音乐源', style: TextStyle().useSystemChineseFont()),
        content: Text(
          '是否立即启用音乐源 "$sourceName"？',
          style: TextStyle().useSystemChineseFont(),
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('稍后', style: TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            child: Text('启用', style: TextStyle().useSystemChineseFont()),
            onPressed: () {
              Navigator.of(context).pop();
              _activateSource(sourceId);
            },
          ),
        ],
      ),
    );
  }
}

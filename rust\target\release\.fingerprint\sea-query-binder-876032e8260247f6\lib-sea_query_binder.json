{"rustc": 1842507548689473721, "features": "[\"sqlx\", \"sqlx-any\", \"sqlx-sqlite\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"ipnetwork\", \"mac_address\", \"postgres-array\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"serde_json\", \"sqlx\", \"sqlx-any\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-mac_address\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 2287068951637276832, "profile": 16503403049695105087, "path": 17016106805425870978, "deps": [[595572719081355498, "sea_query", false, 10416258748148299576], [10632374999838431203, "sqlx", false, 14994305794580139474]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sea-query-binder-876032e8260247f6\\dep-lib-sea_query_binder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
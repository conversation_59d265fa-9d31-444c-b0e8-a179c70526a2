{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 17257705230225558938, "path": 13349765021836815108, "deps": [[530211389790465181, "hex", false, 12472180253276544719], [996810380461694889, "sqlx_core", false, 2065236453459297426], [2713742371683562785, "syn", false, 13650516424722858390], [3060637413840920116, "proc_macro2", false, 9130031249411034821], [3150220818285335163, "url", false, 9325070482304056611], [3405707034081185165, "dotenvy", false, 12132916880984188140], [3722963349756955755, "once_cell", false, 7055297496807936537], [5236433071915784494, "sha2", false, 1459502178408953817], [8045585743974080694, "heck", false, 5799613043833407646], [8569119365930580996, "serde_json", false, 16828220892324081039], [8858041990736880586, "tokio", false, 5750626673386747935], [9689903380558560274, "serde", false, 11175561309716425365], [11637111059468842078, "tempfile", false, 6688883774135725192], [11838249260056359578, "sqlx_sqlite", false, 2868570025238610397], [13678188063678887560, "either", false, 12906008158377540667], [17990358020177143287, "quote", false, 17875812338056798027]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-macros-core-c19e77ddf9eac5cb\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
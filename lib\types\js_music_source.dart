﻿import 'dart:convert';
import 'dart:io';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:http/http.dart' as http;

// 本地PlayInfo和Quality类定义
class Quality {
  final String short;
  final int? level;
  final int? bitrate;
  final String? format;
  final int? size;

  Quality({
    required this.short,
    this.level,
    this.bitrate,
    this.format,
    this.size,
  });

  Map<String, dynamic> toJson() {
    return {
      'short': short,
      'level': level,
      'bitrate': bitrate,
      'format': format,
      'size': size,
    };
  }

  factory Quality.fromJson(Map<String, dynamic> json) {
    return Quality(
      short: json['short'] ?? 'standard',
      level: json['level'],
      bitrate: json['bitrate'],
      format: json['format'],
      size: json['size'],
    );
  }
}

class PlayInfo {
  final String uri;
  final Quality quality;

  PlayInfo({
    required this.uri,
    required this.quality,
  });

  Map<String, dynamic> toJson() {
    return {
      'uri': uri,
      'quality': quality.toJson(),
    };
  }

  factory PlayInfo.fromJson(Map<String, dynamic> json) {
    return PlayInfo(
      uri: json['uri'] ?? '',
      quality: Quality.fromJson(json['quality'] ?? {}),
    );
  }
}

/// 创建JS音乐源执行器
Future<bool> createJsMusicSourceExecutor({
  required String name,
  required String version,
  required String author,
  required String description,
  required String sourceCode,
}) async {
  // 临时实现：总是返回true
  return true;
}

/// 销毁JS音乐源执行器
Future<bool> destroyJsMusicSourceExecutor() async {
  // 临时实现：总是返回true
  return true;
}

/// 检查JS音乐源执行器是否存在
Future<bool> hasJsMusicSourceExecutor() async {
  // 临时实现：总是返回true
  return true;
}

/// 搜索音乐
Future<String> jsSearchMusic({
  required String keyword,
  required int limit,
}) async {
  // 简化实现：直接从网易云音乐API搜索
  globalTalker.info('[jsSearchMusic] 搜索: $keyword, limit: $limit');

  try {
    final response = await http.get(
      Uri.parse('https://wyy-api-three.vercel.app/search?keywords=${Uri.encodeComponent(keyword)}&limit=$limit'),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final songs = data['result']?['songs'] as List?;

      if (songs != null) {
        final results = songs.map((song) => {
          'id': song['id'],
          'name': song['name'],
          'artist': song['ar']?.map((a) => a['name']).join(', ') ?? 'Unknown',
          'album': song['al']?['name'] ?? 'Unknown',
          'duration': song['dt'] ?? 0,
        }).toList();

        globalTalker.info('[jsSearchMusic] 搜索成功，找到 ${results.length} 首歌曲');
        return jsonEncode(results);
      }
    }

    globalTalker.error('[jsSearchMusic] API响应无效: ${response.statusCode}');
    return '[]';
  } catch (e) {
    globalTalker.error('[jsSearchMusic] 搜索失败: $e');
    return '[]';
  }
}

/// 获取音乐播放信息
Future<String?> jsGetMusicPlayInfo({
  required String source,
  required String extra,
}) async {
  // 简化实现：直接从网易云音乐API获取播放链接
  globalTalker.info('[jsGetMusicPlayInfo] 调用: source=$source, extra=$extra');

  try {
    // 解析extra参数获取歌曲ID
    final extraData = jsonDecode(extra);
    final songId = extraData['id']?.toString();

    if (songId == null) {
      globalTalker.error('[jsGetMusicPlayInfo] 无法获取歌曲ID');
      return null;
    }

    // 调用网易云音乐API获取播放链接
    final response = await http.get(
      Uri.parse('https://wyy-api-three.vercel.app/song/url/v1?id=$songId&level=standard'),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final songData = data['data']?[0];

      if (songData != null && songData['url'] != null) {
        final result = jsonEncode({
          'url': songData['url'],
          'quality': 'standard',
          'bitrate': songData['br'] ?? 128000,
          'format': songData['type'] ?? 'mp3',
          'size': songData['size'] ?? 0,
        });

        globalTalker.info('[jsGetMusicPlayInfo] 成功获取播放链接');
        return result;
      }
    }

    globalTalker.error('[jsGetMusicPlayInfo] API响应无效: ${response.statusCode}');
    return null;
  } catch (e) {
    globalTalker.error('[jsGetMusicPlayInfo] 获取播放链接失败: $e');
    return null;
  }
}

/// 获取歌词
Future<String?> jsGetLyric({
  required String source,
  required String extra,
}) async {
  // 临时实现：返回null
  return null;
}

/// JS音乐源信息
class JsMusicSource {
  final String name;
  final String version;
  final String author;
  final String description;
  final String sourceCode;

  const JsMusicSource({
    required this.name,
    required this.version,
    required this.author,
    required this.description,
    required this.sourceCode,
  });

  /// 从JSON创建JS音乐源
  factory JsMusicSource.fromJson(Map<String, dynamic> json) {
    return JsMusicSource(
      name: json['name'] ?? 'Unknown',
      version: json['version'] ?? '1.0.0',
      author: json['author'] ?? 'Unknown',
      description: json['description'] ?? '',
      sourceCode: json['sourceCode'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'version': version,
      'author': author,
      'description': description,
      'sourceCode': sourceCode,
    };
  }

  /// 从文件加载JS音乐源
  static Future<JsMusicSource> fromFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('文件不存在: $filePath');
      }

      final content = await file.readAsString();

      // 尝试解析为JSON格式
      try {
        final json = jsonDecode(content);
        return JsMusicSource.fromJson(json);
      } catch (e) {
        // 如果不是JSON格式，则作为纯JS代码处理，尝试从代码中提取元数据
        final fileName = filePath.split('/').last.split('\\').last;
        final metadata = _extractMetadataFromJS(content);

        return JsMusicSource(
          name: metadata['name'] ?? fileName.replaceAll('.js', ''),
          version: metadata['version'] ?? '1.0.0',
          author: metadata['author'] ?? 'Unknown',
          description: metadata['description'] ?? '从文件加载的JS音乐源',
          sourceCode: content,
        );
      }
    } catch (e) {
      throw Exception('加载JS音乐源失败: $e');
    }
  }

  /// 从URL加载JS音乐源
  static Future<JsMusicSource> fromUrl(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      try {
        final json = jsonDecode(response.body);
        return JsMusicSource.fromJson(json);
      } catch (e) {
        // 如果不是JSON格式，则作为纯JS代码处理，尝试从代码中提取元数据
        final fileName = url.split('/').last;
        final metadata = _extractMetadataFromJS(response.body);

        return JsMusicSource(
          name: metadata['name'] ?? fileName.replaceAll('.js', ''),
          version: metadata['version'] ?? '1.0.0',
          author: metadata['author'] ?? 'Unknown',
          description: metadata['description'] ?? '从URL加载的JS音乐源',
          sourceCode: response.body,
        );
      }
    } catch (e) {
      throw Exception('从URL加载JS音乐源失败: $e');
    }
  }

  /// 从JS代码中提取元数据
  static Map<String, String> _extractMetadataFromJS(String jsCode) {
    final metadata = <String, String>{};

    try {
      // 方法1: 从注释中提取信息
      final lines = jsCode.split('\n').take(20); // 只检查前20行
      for (final line in lines) {
        // 提取作者信息
        if (line.contains('作者:') || line.contains('Author:')) {
          final authorMatch = RegExp(r'(?:作者|Author)[:：]\s*(.+)').firstMatch(line);
          if (authorMatch != null) {
            metadata['author'] = authorMatch.group(1)?.trim() ?? '';
          }
        }
        // 提取版本信息
        if (line.contains('版本:') || line.contains('Version:')) {
          final versionMatch = RegExp(r'(?:版本|Version)[:：]\s*(.+)').firstMatch(line);
          if (versionMatch != null) {
            metadata['version'] = versionMatch.group(1)?.trim() ?? '';
          }
        }
        // 提取名称信息
        if (line.contains('名称:') || line.contains('Name:')) {
          final nameMatch = RegExp(r'(?:名称|Name)[:：]\s*(.+)').firstMatch(line);
          if (nameMatch != null) {
            metadata['name'] = nameMatch.group(1)?.trim() ?? '';
          }
        }
        // 提取描述信息
        if (line.contains('描述:') || line.contains('Description:')) {
          final descMatch = RegExp(r'(?:描述|Description)[:：]\s*(.+)').firstMatch(line);
          if (descMatch != null) {
            metadata['description'] = descMatch.group(1)?.trim() ?? '';
          }
        }
      }

      // 方法2: 从CONFIG对象中提取信息
      if (jsCode.contains('const CONFIG')) {
        // 简单的字符串匹配方式，分别处理单引号和双引号
        var nameMatch = RegExp(r"name:\s*'([^']+)'").firstMatch(jsCode);
        nameMatch ??= RegExp(r'name:\s*"([^"]+)"').firstMatch(jsCode);
        if (nameMatch != null && metadata['name']?.isEmpty != false) {
          metadata['name'] = nameMatch.group(1)?.trim() ?? '';
        }

        var versionMatch = RegExp(r"version:\s*'([^']+)'").firstMatch(jsCode);
        versionMatch ??= RegExp(r'version:\s*"([^"]+)"').firstMatch(jsCode);
        if (versionMatch != null && metadata['version']?.isEmpty != false) {
          metadata['version'] = versionMatch.group(1)?.trim() ?? '';
        }

        var authorMatch = RegExp(r"author:\s*'([^']+)'").firstMatch(jsCode);
        authorMatch ??= RegExp(r'author:\s*"([^"]+)"').firstMatch(jsCode);
        if (authorMatch != null && metadata['author']?.isEmpty != false) {
          metadata['author'] = authorMatch.group(1)?.trim() ?? '';
        }

        var descMatch = RegExp(r"description:\s*'([^']+)'").firstMatch(jsCode);
        descMatch ??= RegExp(r'description:\s*"([^"]+)"').firstMatch(jsCode);
        if (descMatch != null && metadata['description']?.isEmpty != false) {
          metadata['description'] = descMatch.group(1)?.trim() ?? '';
        }
      }

    } catch (e) {
      // 如果解析失败，返回空的metadata，使用默认值
    }

    return metadata;
  }

  // 内置播放源已移除，只支持外部JS音乐源
}

/// JS音乐源执行器 - 使用Rust后端
class JsMusicSourceExecutor {
  final JsMusicSource musicSource;
  bool _isInitialized = false;
  Map<String, String>? _lastLyricData; // 存储最后获取的歌词数据

  JsMusicSourceExecutor(this.musicSource);

  /// 初始化执行器
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      try {
        final result = await createJsMusicSourceExecutor(
          name: musicSource.name,
          version: musicSource.version,
          author: musicSource.author,
          description: musicSource.description,
          sourceCode: musicSource.sourceCode,
        );

        if (result) {
          _isInitialized = true;
          globalTalker.info('[JS Source] 执行器初始化成功: ${musicSource.name}');
        } else {
          throw Exception('Failed to initialize JS executor');
        }
      } catch (e) {
        globalTalker.error('[JS Source] 执行器初始化失败: $e');
        rethrow;
      }
    }
  }

  /// 搜索音乐
  Future<List<Map<String, dynamic>>> searchMusic(String keyword, {int limit = 10}) async {
    try {
      await _ensureInitialized();
      globalTalker.info('[JS Source] 搜索音乐: $keyword (limit: $limit)');

      final resultJson = await jsSearchMusic(keyword: keyword, limit: limit);
      final List<dynamic> resultList = jsonDecode(resultJson);
      return resultList.cast<Map<String, dynamic>>();
    } catch (e) {
      globalTalker.error('[JS Source] 搜索音乐失败: $e');
      return [];
    }
  }



  /// 获取音乐播放信息
  Future<PlayInfo?> getMusicPlayInfo(String source, String extra) async {
    try {
      await _ensureInitialized();
      globalTalker.info('[JS Source] 获取播放信息: $source, $extra');

      final resultJson = await jsGetMusicPlayInfo(source: source, extra: extra);
      globalTalker.info('[JS Source] jsGetMusicPlayInfo返回: $resultJson');

      if (resultJson != null) {
        final Map<String, dynamic> result = jsonDecode(resultJson);
        globalTalker.info('[JS Source] 解析JSON结果: $result');

        // 从结果中提取播放信息
        final String? url = result['url'];
        if (url != null && url.isNotEmpty) {
          // 创建默认音质对象
          final quality = Quality(
            short: result['quality'] ?? 'standard',
            bitrate: result['bitrate'],
            format: result['format'],
            size: result['size'],
          );

          final playInfo = PlayInfo(
            uri: url,
            quality: quality,
          );

          globalTalker.info('[JS Source] 创建PlayInfo成功: uri=${playInfo.uri}, quality=${playInfo.quality.short}');
          return playInfo;
        } else {
          globalTalker.warning('[JS Source] URL为空或null');
        }
      } else {
        globalTalker.warning('[JS Source] jsGetMusicPlayInfo返回null');
      }
      return null;
    } catch (e) {
      globalTalker.error('[JS Source] 获取播放信息失败: $e');
      return null;
    }
  }



  /// 获取最后一次请求的歌词数据
  Map<String, String>? getLastLyricData() {
    return _lastLyricData;
  }



  /// 获取歌词
  Future<String?> getLyric(String source, String extra) async {
    try {
      await _ensureInitialized();
      globalTalker.info('[JS Source] 获取歌词: $source, $extra');

      return await jsGetLyric(source: source, extra: extra);
    } catch (e) {
      globalTalker.error('[JS Source] 获取歌词失败: $e');
      return null;
    }
  }

  /// 释放资源
  void dispose() {
    if (_isInitialized) {
      destroyJsMusicSourceExecutor();
      _isInitialized = false;
      globalTalker.info('[JS Source] 释放资源');
    }
  }
}

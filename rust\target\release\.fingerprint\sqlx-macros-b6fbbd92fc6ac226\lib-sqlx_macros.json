{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"default\", \"json\", \"migrate\", \"sqlite\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 17257705230225558938, "path": 15640342988816685036, "deps": [[996810380461694889, "sqlx_core", false, 2065236453459297426], [2713742371683562785, "syn", false, 13650516424722858390], [3060637413840920116, "proc_macro2", false, 9130031249411034821], [15733334431800349573, "sqlx_macros_core", false, 10244054352612579436], [17990358020177143287, "quote", false, 17875812338056798027]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-macros-b6fbbd92fc6ac226\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
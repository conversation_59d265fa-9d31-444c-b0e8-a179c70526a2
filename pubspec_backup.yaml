name: app_rhyme
description: "A Powerfull CrossPlatform Music App"
publish_to: "none"

version: 1.0.9

environment:
  sdk: ">=3.3.4 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  rust_lib_app_rhyme:
    path: rust_builder

  cupertino_icons: ^1.0.6
  flutter_rust_bridge: 2.0.0
  interactive_slider:
    git: https://github.com/canxin121/interactive_slider
  dismissible_page: ^1.0.2
  get: ^4.6.6
  audio_service: ^0.18.13
  infinite_scroll_pagination: ^4.0.0
  path_provider: ^2.1.3
  chinese_font_library: ^1.1.0
  flutter_lyric:
    git: https://github.com/canxin121/flutter_lyric
  talker: ^4.1.5
  just_audio_background: ^0.0.1-beta.11
  audio_session: ^0.1.19
  pull_down_button: ^0.9.4

  synchronized: ^3.1.0+1
  just_audio: ^0.9.38
  extended_image: ^8.2.1
  http: ^1.1.0
  connectivity_plus: ^6.0.3
  permission_handler: ^11.3.1
  flutter_keyboard_visibility: ^6.0.0
  # 桌面版专用依赖
  bitsdojo_window: ^0.1.6
  file_picker: ^8.0.3
  url_launcher: ^6.3.0
  back_button_interceptor: ^7.0.3
  reorderables: ^0.6.0
  drag_select_grid_view: ^0.6.2
  package_info_plus: ^8.0.0
  talker_flutter: ^4.1.5
  # 手机版专用依赖
  flutter_exit_app: ^1.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/

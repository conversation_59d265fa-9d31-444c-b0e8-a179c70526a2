import 'dart:io';
import 'dart:convert';

import 'package:app_rhyme/src/rust/api/cache/music_cache.dart';
import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:audio_service/audio_service.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/utils/const_vars.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/quality_picker.dart';
import 'package:app_rhyme/utils/source_helper.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:http/http.dart' as http;

// 这个结构代表了待播音乐的信息
class MusicContainer {
  late MusicAggregatorW aggregator;
  late MusicW currentMusic;
  late MusicInfo info;
  late String? extra;
  // 从Api or 本地获取的真实待播放的音质信息
  late Rx<Quality?> currentQuality;
  PlayInfo? playInfo;
  // 待播放的音频资源
  late AudioSource audioSource;
  // 已经使用过的音乐源，用于自动换源时选择下一个源
  List<String> usedSources = [];
  // 上次更新时间，用于判断是否需要更新
  DateTime lastUpdate = DateTime(1999);

  MusicContainer(MusicAggregatorW aggregator_) {
    aggregator = aggregator_;

    // 异步设置网易云音乐为默认源
    _initializePreferredSource();

    currentMusic = aggregator.getDefaultMusic();
    info = currentMusic.getMusicInfo();
    _updateQuality();
    audioSource = AudioSource.asset("assets/blank.mp3", tag: _toMediaItem());
  }

  /// 异步设置网易云音乐为默认源
  void _initializePreferredSource() {
    final availableSources = aggregator.getAvailableSources();
    if (availableSources.contains(sourceWangYi)) {
      // 异步设置默认源，不阻塞构造函数
      Future.microtask(() async {
        try {
          await aggregator.setDefaultSource(source: sourceWangYi);
          currentMusic = aggregator.getDefaultMusic();
          info = currentMusic.getMusicInfo();
          _updateQuality();
          globalTalker.info('[MusicContainer] 已设置网易云音乐为默认源: ${info.name}');
        } catch (e) {
          globalTalker.warning('[MusicContainer] 设置网易云音乐源失败: $e');
        }
      });
    }
  }

  // 使上次更新时间过期
  setOutdate() {
    lastUpdate = DateTime(1999);
  }

  String toCacheFileName() {
    return "${info.name}_${info.artist.join(",")}_${currentQuality.value!.short}.${currentQuality.value!.format ?? "unknown"}";
  }

  // 检查音乐是否需要更新
  bool shouldUpdate() {
    try {
      return (audioSource as ProgressiveAudioSource)
              .uri
              .path
              .contains("/assets/") ||
          DateTime.now().difference(lastUpdate).abs().inSeconds >= 1800;
    } catch (_) {
      return true;
    }
  }

  // 是否有缓存
  Future<bool> hasCache() async {
    try {
      return hasCachePlayinfo(musicInfo: info);
    } catch (e) {
      return false;
    }
  }

  // 更新音乐内部的播放信息和音频资源
  // quality: 指定音质，如果不指定则使用默认音质
  // 会在 主动获取 或者 LazyLoad 时使用
  // 如果获取失败，则会尝试换源
  // 如果换源后仍失败，则会返回false
  Future<bool> updateAll([Quality? quality]) async {
    bool success = await _updateAudioSource(quality);
    if (success) {
      await _updateLyric();
    }
    return success;
  }

  Future<PlayInfo?> getCurrentMusicPlayInfo([Quality? quality_]) async {
    // 更新当前音质, 每次都更新以适配网络变化
    _updateQuality(quality_);

    late Quality finalQuality;
    if (quality_ != null) {
      finalQuality = quality_;
    } else if (currentQuality.value != null) {
      finalQuality = currentQuality.value!;
    } else {
      LogToast.error("获取播放信息失败", "未找到可用音质",
          "[getCurrentMusicPlayInfo] Failed to get play info, no quality found");
      return null;
    }
    // 更新extra信息
    extra = currentMusic.getExtraInfo(quality: finalQuality);

    // // 有本地缓存直接返回
    try {
      playInfo = await getCachePlayinfo(musicInfo: info);
      if (playInfo != null) {
        globalTalker.info("[getCurrentMusicPlayInfo] 使用缓存歌曲: ${info.name}");
        currentQuality.value = playInfo!.quality;
        return playInfo!;
      }
      // ignore: empty_catches
    } catch (e) {}

    // 检查是否是内置网易云音乐源
    // 1. 直接检查source是否为netease
    // 2. 检查是否是网易云音乐ID（纯数字且长度合适）
    // 3. 检查source是否为WangYi（网易云的内部标识）
    bool isNeteaseMusic = info.source == 'netease' ||
        info.source == 'WangYi' ||
        (extra != null && _isNeteaseId(extra!));

    globalTalker.info('[MusicContainer] 检测音乐源: ${info.name}, Source: ${info.source}, Extra: $extra, IsNetease: $isNeteaseMusic');

    if (isNeteaseMusic) {
      playInfo = await _getNeteasePlayInfo();
      if (playInfo != null) {
        currentQuality.value = playInfo!.quality;
        globalTalker.info(
            "[getCurrentMusicPlayInfo] 使用内置网易云API获取playinfo: [${info.source}]${info.name}");
        return playInfo;
      }
    }

    // 没有本地缓存，也没有第三方api，直接返回null
    if (globalConfig.externApi == null) {
      // 如果是网易云音乐但API调用失败，给出更具体的错误信息
      if (isNeteaseMusic) {
        LogToast.error("获取播放信息失败", "内置网易云音乐API调用失败，请检查网络连接",
            "[getCurrentMusicPlayInfo] Built-in Netease API failed");
      } else {
        // 检查是否有网易云音乐源可以切换
        final availableSources = aggregator.getAvailableSources();
        if (availableSources.contains(sourceWangYi)) {
          // 有网易云音乐源，不显示错误提示，直接尝试切换源
          globalTalker.info("[getCurrentMusicPlayInfo] 检测到网易云音乐源，尝试切换");
        } else {
          // 未导入第三方音乐源，应当toast提示用户
          LogToast.error("获取播放信息失败", "未导入第三方音乐源，无法在线获取播放信息",
              "[getCurrentMusicPlayInfo] Failed to get play info, no extern api");
        }
      }
      return null;
    }

    // 有第三方api，使用api进行请求
    playInfo =
        await globalExternApiEvaler!.getMusicPlayInfo(info.source, extra!);

    // 如果第三方api查找不到，直接返回null
    if (playInfo == null) {
      globalTalker.error(
          "[getCurrentMusicPlayInfo] 第三方音乐源无法获取到playinfo: [${info.source}]${info.name}");
      return null;
    } else {
      currentQuality.value = playInfo!.quality;
      globalTalker.info(
          "[getCurrentMusicPlayInfo] 使用第三方Api请求获取playinfo: [${info.source}]${info.name}");
      return playInfo;
    }
  }

  // 将音乐信息转化为MediaItem, 用于AudioService在系统显示音频信息
  MediaItem _toMediaItem() {
    Uri? artUri;
    if (info.artPic != null) {
      artUri = Uri.parse(info.artPic!);
    } else {
      artUri = null;
    }
    return MediaItem(
        id: extra.hashCode.toString(),
        title: info.name,
        album: info.album,
        artUri: artUri,
        artist: info.artist.join(","));
  }

  Future<void> _updateLyric() async {
    if (info.lyric == null || info.lyric!.isEmpty) {
      try {
        var lyric = await aggregator.fetchLyric();
        globalTalker.info("[MusicContainer] 更新 '${info.name}' 歌词成功");
        info.lyric = lyric;
      } catch (e) {
        LogToast.error("更新歌词失败", "在线更新歌词失败: $e",
            "[MusicContainer] Failed to update lyric: $e");
        info.lyric = "[00:00.00]获取歌词失败";
      }
    }
  }

  Future<bool> _updateAudioSource([Quality? quality]) async {
    lastUpdate = DateTime.now();
    if (quality != null) extra = currentMusic.getExtraInfo(quality: quality);
    while (true) {
      try {
        playInfo = await getCurrentMusicPlayInfo(quality);
      } catch (e) {
        playInfo = null;
      }
      if (playInfo != null) {
        // 更新当前音质
        currentQuality.value = playInfo!.quality;

        if (playInfo!.uri.contains("http")) {
          if ((Platform.isIOS || Platform.isMacOS) &&
              ((playInfo!.quality.format != null &&
                      playInfo!.quality.format!.contains("flac")) ||
                  (playInfo!.quality.short.contains("flac")))) {
            audioSource = ProgressiveAudioSource(Uri.parse(playInfo!.uri),
                tag: _toMediaItem(),
                options: const ProgressiveAudioSourceOptions(
                    darwinAssetOptions: DarwinAssetOptions(
                        preferPreciseDurationAndTiming: true)));
          } else {
            audioSource =
                AudioSource.uri(Uri.parse(playInfo!.uri), tag: _toMediaItem());
          }
        } else {
          if ((Platform.isIOS || Platform.isMacOS) &&
              ((playInfo!.quality.format != null &&
                      playInfo!.quality.format!.contains("flac")) ||
                  (playInfo!.quality.short.contains("flac")))) {
            audioSource = ProgressiveAudioSource(Uri.file(playInfo!.uri),
                tag: _toMediaItem(),
                options: const ProgressiveAudioSourceOptions(
                    darwinAssetOptions: DarwinAssetOptions(
                        preferPreciseDurationAndTiming: true)));
          } else {
            audioSource = AudioSource.file(playInfo!.uri, tag: _toMediaItem());
          }
        }
        globalTalker.info("[MusicContainer] 更新 '${info.name}' 音频资源成功");
        return true;
      } else {
        // 检查是否有网易云音乐源可以切换
        final availableSources = aggregator.getAvailableSources();
        if (availableSources.contains(sourceWangYi) && !usedSources.contains(sourceWangYi)) {
          // 有网易云音乐源且未使用过，不显示错误提示
          globalTalker.info("[MusicContainer] 检测到网易云音乐源，尝试切换到内置API");
        } else {
          LogToast.error("更新播放资源失败", "${info.name}更新播放资源失败, 尝试换源播放",
              "[MusicContainer] Failed to update audio source, try to change source");
        }
        bool changed = await _changeSource();
        if (!changed) {
          return false;
        }
      }
    }
  }

  Future<bool> _changeSource([String? source]) async {
    // 换源表明弃用当前源，将其移到usedSource中
    usedSources.add(currentMusic.source());
    // 根据usedSource来获得下一个源
    source ??= nextSource(usedSources);

    if (source != null) {
      try {
        var musics = await aggregator.fetchMusics(sources: [source]);
        if (musics.isEmpty) {
          LogToast.error(
              "切换音乐源失败",
              "${info.name}切换音乐源失败: 在$source查找不到'${info.name}'歌曲.",
              "[MusicContainer] Failed to change music source: Cannot find '${info.name}' in $source");
          return false;
        }
        await aggregator.setDefaultSource(source: source);
        currentMusic = aggregator.getDefaultMusic();
        info = currentMusic.getMusicInfo();
        extra = currentMusic.getExtraInfo(quality: info.defaultQuality!);
        audioSource =
            AudioSource.asset("assets/blank.mp3", tag: _toMediaItem());
        LogToast.info("切换音乐源成功", "${info.name}默认音源切换为$source",
            "[MusicContainer] Successfully changed music source to $source");
      } catch (e) {
        LogToast.error("切换音乐源失败", "${info.name}切换音乐源失败: $e",
            "[MusicContainer] Failed to change music source: $e");

        return false;
      }
      return true;
    } else {
      return false;
    }
  }

  void _updateQuality([Quality? quality]) {
    if (quality != null) {
      currentQuality.value = quality;
      extra = currentMusic.getExtraInfo(quality: quality);
    } else {
      if (info.qualities.isNotEmpty) {
        currentQuality = autoPickQuality(info.qualities).obs;
        extra = currentMusic.getExtraInfo(quality: currentQuality.value!);
      } else {
        currentQuality.value = null;
        extra = null;
      }
    }
  }

  /// 获取网易云音乐播放信息
  Future<PlayInfo?> _getNeteasePlayInfo() async {
    try {
      if (extra == null || extra!.isEmpty) {
        globalTalker.error('[MusicContainer] 网易云音乐ID为空');
        return null;
      }

      // 解析extra中的音乐ID
      String musicId;
      try {
        final extraData = json.decode(extra!);
        if (extraData is Map && extraData.containsKey('id')) {
          musicId = extraData['id'].toString();
        } else {
          musicId = extra!;
        }
      } catch (e) {
        // 如果解析失败，直接使用extra作为ID
        musicId = extra!;
      }

      globalTalker.info('[MusicContainer] 获取网易云音乐播放信息: ${info.name}, ID: $musicId, Source: ${info.source}');

      final url = Uri.parse('https://api.kxzjoker.cn/api/163_music');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'ids': musicId,
          'level': 'jymaster', // 默认超清母带
          'type': 'json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      final data = json.decode(response.body);

      if (data['status'] != 200) {
        throw Exception('API错误: ${data['message'] ?? '未知错误'}');
      }

      final String playUrl = data['url']?.toString() ?? '';
      if (playUrl.isEmpty) {
        throw Exception('无法获取播放链接');
      }

      // 创建PlayInfo对象
      final playInfo = PlayInfo(
        uri: playUrl,
        quality: Quality(
          short: 'jymaster',
          level: data['level']?.toString() ?? '超清母带',
          bitrate: _getQualityBitrate(data['level']?.toString() ?? 'jymaster'),
          format: _getFormatFromUrl(playUrl),
          size: data['size']?.toString(),
        ),
      );

      globalTalker.info('[MusicContainer] 网易云音乐播放信息获取成功: ${info.name}');
      return playInfo;

    } catch (e) {
      globalTalker.error('[MusicContainer] 获取网易云音乐播放信息失败: $e');
      LogToast.error('获取播放信息失败', '无法获取 ${info.name} 的播放链接: $e',
          '[MusicContainer] Failed to get netease play info: $e');
      return null;
    }
  }

  /// 根据音质获取比特率
  int _getQualityBitrate(String quality) {
    switch (quality.toLowerCase()) {
      case 'standard':
      case '标准音质':
        return 128;
      case 'exhigh':
      case '极高品质':
        return 320;
      case 'lossless':
      case '无损音质':
        return 999;
      case 'hires':
      case 'hi-res音质':
        return 1411;
      case 'jyeffect':
      case '高清环绕声':
        return 1411;
      case 'sky':
      case '沉浸环绕声':
        return 1411;
      case 'jymaster':
      case '超清母带':
        return 1411;
      default:
        return 320;
    }
  }

  /// 从URL获取格式
  String? _getFormatFromUrl(String url) {
    if (url.isEmpty) return null;

    try {
      final uri = Uri.parse(url);
      final path = uri.path.toLowerCase();
      if (path.endsWith('.mp3')) return 'mp3';
      if (path.endsWith('.flac')) return 'flac';
      if (path.endsWith('.m4a')) return 'm4a';
    } catch (e) {
      // 解析失败
    }

    return 'mp3'; // 默认
  }

  /// 检查是否是网易云音乐ID
  bool _isNeteaseId(String id) {
    // 网易云音乐ID通常是纯数字，长度在6-12位之间
    if (id.isEmpty) return false;

    // 尝试解析JSON格式的extra
    try {
      final extraData = json.decode(id);
      if (extraData is Map && extraData.containsKey('id')) {
        final musicId = extraData['id'].toString();
        // 检查是否为纯数字
        final numericRegex = RegExp(r'^\d+$');
        if (numericRegex.hasMatch(musicId) && musicId.length >= 6 && musicId.length <= 12) {
          return true;
        }
      }
    } catch (e) {
      // 不是JSON格式，继续检查是否为纯数字ID
    }

    // 检查是否为纯数字
    final numericRegex = RegExp(r'^\d+$');
    if (!numericRegex.hasMatch(id)) return false;

    // 检查长度（网易云音乐ID通常在6-12位）
    if (id.length < 6 || id.length > 12) return false;

    return true;
  }
}

import 'package:flutter/cupertino.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/types/js_source_manager.dart';
import 'package:app_rhyme/pages/js_source_management_page.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:app_rhyme/utils/global_vars.dart';

class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  State<MorePage> createState() => _MorePageState();
}

class _MorePageState extends State<MorePage> {
  @override
  void initState() {
    super.initState();
    // 监听源管理器变化
    JsSourceManager.addListener(_onSourcesChanged);
  }

  @override
  void dispose() {
    JsSourceManager.removeListener(_onSourcesChanged);
    super.dispose();
  }

  void _onSourcesChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
    final backgroundColor = isDarkMode ? CupertinoColors.black : CupertinoColors.white;
    final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
    final iconColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
    
    final activeSource = JsSourceManager.activeSource;

    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        middle: Text(
          '更多设置',
          style: TextStyle(color: textColor).useSystemChineseFont(),
        ),
      ),
      child: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // 应用信息卡片
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode ? CupertinoColors.systemGrey6.darkColor : CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        CupertinoIcons.info_circle,
                        color: CupertinoColors.systemBlue,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '应用信息',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: textColor,
                        ).useSystemChineseFont(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('应用名称', 'AppRhyme', textColor),
                  const SizedBox(height: 8),
                  _buildInfoRow('版本号', 'v1.0.9', textColor),
                  const SizedBox(height: 8),
                  _buildInfoRow('当前活跃源', activeSource?.name ?? '无', textColor),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // JS源管理
            Text(
              'JS音乐源管理',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: textColor,
              ).useSystemChineseFont(),
            ),
            const SizedBox(height: 12),
            
            Container(
              decoration: BoxDecoration(
                color: isDarkMode ? CupertinoColors.systemGrey6.darkColor : CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(12),
              ),
              child: CupertinoListTile(
                leading: Icon(
                  CupertinoIcons.music_note_list,
                  color: CupertinoColors.systemBlue,
                ),
                title: Text(
                  '管理JS音乐源',
                  style: TextStyle(color: textColor).useSystemChineseFont(),
                ),
                subtitle: Text(
                  '添加、删除、切换音乐源',
                  style: TextStyle(
                    color: textColor.withOpacity(0.6),
                    fontSize: 14,
                  ).useSystemChineseFont(),
                ),
                trailing: Icon(
                  CupertinoIcons.chevron_right,
                  color: iconColor.withOpacity(0.6),
                ),
                onTap: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => const JsSourceManagementPage(),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 调试工具
            Text(
              '调试工具',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: textColor,
              ).useSystemChineseFont(),
            ),
            const SizedBox(height: 12),
            
            Container(
              decoration: BoxDecoration(
                color: isDarkMode ? CupertinoColors.systemGrey6.darkColor : CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(12),
              ),
              child: CupertinoListTile(
                leading: Icon(
                  CupertinoIcons.doc_text,
                  color: CupertinoColors.systemOrange,
                ),
                title: Text(
                  '查看日志',
                  style: TextStyle(color: textColor).useSystemChineseFont(),
                ),
                subtitle: Text(
                  '查看应用运行日志',
                  style: TextStyle(
                    color: textColor.withOpacity(0.6),
                    fontSize: 14,
                  ).useSystemChineseFont(),
                ),
                trailing: Icon(
                  CupertinoIcons.chevron_right,
                  color: iconColor.withOpacity(0.6),
                ),
                onTap: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => TalkerScreen(talker: globalTalker),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 40),
            
            // 底部信息
            Center(
              child: Column(
                children: [
                  Text(
                    'AppRhyme - JS音乐源管理系统',
                    style: TextStyle(
                      color: textColor.withOpacity(0.5),
                      fontSize: 14,
                    ).useSystemChineseFont(),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '专注于JS音乐源的多源管理',
                    style: TextStyle(
                      color: textColor.withOpacity(0.4),
                      fontSize: 12,
                    ).useSystemChineseFont(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, Color textColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: textColor.withOpacity(0.8),
            fontSize: 14,
          ).useSystemChineseFont(),
        ),
        Text(
          value,
          style: TextStyle(
            color: textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ).useSystemChineseFont(),
        ),
      ],
    );
  }
}

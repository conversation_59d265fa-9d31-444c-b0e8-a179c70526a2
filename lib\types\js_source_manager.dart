import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:app_rhyme/types/js_music_source.dart';
import 'package:app_rhyme/utils/global_vars_simplified.dart';

/// JS音乐源管理器
class JsSourceManager {
  static const String _sourcesFileName = 'js_music_sources.json';
  static const String _activeSourceFileName = 'active_js_source.txt';
  
  /// 获取源存储文件路径
  static String get _sourcesFilePath => '$globalDocumentPath/AppRhyme/config/$_sourcesFileName';
  
  /// 获取活跃源存储文件路径
  static String get _activeSourceFilePath => '$globalDocumentPath/AppRhyme/config/$_activeSourceFileName';
  
  /// 存储的JS音乐源列表
  static List<StoredJsSource> _sources = [];
  
  /// 当前活跃的源ID
  static String? _activeSourceId;

  /// 监听器列表
  static final List<VoidCallback> _listeners = [];
  
  /// 获取所有存储的源
  static List<StoredJsSource> get sources => List.unmodifiable(_sources);
  
  /// 获取当前活跃的源ID
  static String? get activeSourceId => _activeSourceId;
  
  /// 获取当前活跃的源
  static StoredJsSource? get activeSource {
    if (_activeSourceId == null) return null;
    try {
      return _sources.firstWhere((source) => source.id == _activeSourceId);
    } catch (e) {
      return null;
    }
  }

  /// 添加监听器
  static void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// 移除监听器
  static void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 通知所有监听器
  static void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }
  
  /// 初始化管理器，从文件加载数据
  static Future<void> initialize() async {
    try {
      await _loadSources();
      await _loadActiveSource();
      
      // 如果有活跃源，自动加载它
      final active = activeSource;
      if (active != null) {
        globalTalker.info("[JsSourceManager] 自动加载活跃源: ${active.source.name}");
      }
    } catch (e) {
      globalTalker.error("[JsSourceManager] 初始化失败: $e");
    }
  }
  
  /// 添加新的JS源
  static Future<String> addSource(JsMusicSource source, {String? customName}) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final storedSource = StoredJsSource(
      id: id,
      name: customName ?? source.name,
      source: source,
      addedAt: DateTime.now(),
      isEnabled: true,
    );
    
    _sources.add(storedSource);
    await _saveSources();
    _notifyListeners();

    globalTalker.info("[JsSourceManager] 添加新源: ${storedSource.name} (ID: $id)");
    return id;
  }
  
  /// 删除JS源
  static Future<bool> removeSource(String id) async {
    final index = _sources.indexWhere((source) => source.id == id);
    if (index == -1) return false;
    
    final removedSource = _sources.removeAt(index);
    await _saveSources();
    _notifyListeners();

    // 如果删除的是活跃源，清除活跃源
    if (_activeSourceId == id) {
      await setActiveSource(null);
    }

    globalTalker.info("[JsSourceManager] 删除源: ${removedSource.name} (ID: $id)");
    return true;
  }
  
  /// 设置活跃源
  static Future<bool> setActiveSource(String? id) async {
    if (id != null && !_sources.any((source) => source.id == id)) {
      return false;
    }
    
    _activeSourceId = id;
    await _saveActiveSource();
    _notifyListeners();

    // 更新全局执行器
    if (id != null) {
      final source = _sources.firstWhere((source) => source.id == id);
      globalTalker.info("[JsSourceManager] 切换到源: ${source.name}");
    } else {
      globalTalker.info("[JsSourceManager] 清除活跃源");
    }

    return true;
  }
  
  /// 更新源信息
  static Future<bool> updateSource(String id, {String? name, bool? isEnabled}) async {
    final index = _sources.indexWhere((source) => source.id == id);
    if (index == -1) return false;
    
    final oldSource = _sources[index];
    _sources[index] = StoredJsSource(
      id: oldSource.id,
      name: name ?? oldSource.name,
      source: oldSource.source,
      addedAt: oldSource.addedAt,
      isEnabled: isEnabled ?? oldSource.isEnabled,
    );
    
    await _saveSources();
    globalTalker.info("[JsSourceManager] 更新源: ${_sources[index].name}");
    return true;
  }
  
  /// 从文件加载源列表
  static Future<void> _loadSources() async {
    try {
      final file = File(_sourcesFilePath);
      if (!await file.exists()) {
        _sources = [];
        return;
      }
      
      final content = await file.readAsString();
      final List<dynamic> jsonList = jsonDecode(content);
      
      _sources = jsonList.map((json) => StoredJsSource.fromJson(json)).toList();
      globalTalker.info("[JsSourceManager] 加载了 ${_sources.length} 个源");
    } catch (e) {
      globalTalker.error("[JsSourceManager] 加载源列表失败: $e");
      _sources = [];
    }
  }
  
  /// 保存源列表到文件
  static Future<void> _saveSources() async {
    try {
      final configDir = Directory('$globalDocumentPath/AppRhyme/config');
      if (!await configDir.exists()) {
        await configDir.create(recursive: true);
      }
      
      final file = File(_sourcesFilePath);
      final jsonList = _sources.map((source) => source.toJson()).toList();
      await file.writeAsString(jsonEncode(jsonList));
      
      globalTalker.info("[JsSourceManager] 保存了 ${_sources.length} 个源");
    } catch (e) {
      globalTalker.error("[JsSourceManager] 保存源列表失败: $e");
    }
  }
  
  /// 从文件加载活跃源ID
  static Future<void> _loadActiveSource() async {
    try {
      final file = File(_activeSourceFilePath);
      if (!await file.exists()) {
        _activeSourceId = null;
        return;
      }
      
      final content = await file.readAsString().then((s) => s.trim());
      _activeSourceId = content.isEmpty ? null : content;
      
      if (_activeSourceId != null) {
        globalTalker.info("[JsSourceManager] 加载活跃源ID: $_activeSourceId");
      }
    } catch (e) {
      globalTalker.error("[JsSourceManager] 加载活跃源失败: $e");
      _activeSourceId = null;
    }
  }
  
  /// 保存活跃源ID到文件
  static Future<void> _saveActiveSource() async {
    try {
      final configDir = Directory('$globalDocumentPath/AppRhyme/config');
      if (!await configDir.exists()) {
        await configDir.create(recursive: true);
      }
      
      final file = File(_activeSourceFilePath);
      await file.writeAsString(_activeSourceId ?? '');
      
      globalTalker.info("[JsSourceManager] 保存活跃源ID: $_activeSourceId");
    } catch (e) {
      globalTalker.error("[JsSourceManager] 保存活跃源失败: $e");
    }
  }
  
  /// 清理所有数据
  static Future<void> clearAll() async {
    _sources.clear();
    _activeSourceId = null;
    
    try {
      final sourcesFile = File(_sourcesFilePath);
      if (await sourcesFile.exists()) {
        await sourcesFile.delete();
      }
      
      final activeFile = File(_activeSourceFilePath);
      if (await activeFile.exists()) {
        await activeFile.delete();
      }
      
      globalTalker.info("[JsSourceManager] 清理所有数据完成");
    } catch (e) {
      globalTalker.error("[JsSourceManager] 清理数据失败: $e");
    }
  }
}

/// 存储的JS音乐源
class StoredJsSource {
  final String id;
  final String name;
  final JsMusicSource source;
  final DateTime addedAt;
  final bool isEnabled;
  
  const StoredJsSource({
    required this.id,
    required this.name,
    required this.source,
    required this.addedAt,
    required this.isEnabled,
  });
  
  /// 从JSON创建
  factory StoredJsSource.fromJson(Map<String, dynamic> json) {
    return StoredJsSource(
      id: json['id'],
      name: json['name'],
      source: JsMusicSource.fromJson(json['source']),
      addedAt: DateTime.parse(json['addedAt']),
      isEnabled: json['isEnabled'] ?? true,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'source': source.toJson(),
      'addedAt': addedAt.toIso8601String(),
      'isEnabled': isEnabled,
    };
  }
}

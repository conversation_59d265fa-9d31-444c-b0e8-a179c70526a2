// 临时生成的绑定文件，等待自动生成
// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import 'dart:async';
import 'dart:convert';
import 'dart:ffi' as ffi;
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// 临时的RustLib类
class RustLib {
  static RustLib? _instance;
  static RustLib get instance => _instance ??= RustLib._();
  
  RustLib._();
  
  late final RustLibApi api = RustLibApiImpl();
  
  static Future<void> init() async {
    // 临时实现：什么都不做
  }
}

// 临时的API接口
abstract class RustLibApi {
  // 临时方法，返回正确的类型
  Future<dynamic> crateApiInitInitBackend({required String storeRoot}) async => null;
  Future<dynamic> crateApiTypesConfigConfigLoad() async => null;
  Future<void> crateApiTypesConfigConfigSave({required dynamic that}) async {}
  
  // 工厂绑定方法
  Future<dynamic> crateApiBindFactoryBindAggregatorOnlineFactoryWSearchMusicAggregator({required dynamic aggregators}) async => null;
  Future<dynamic> crateApiBindFactoryBindOnlineFactoryWGetMusiclistFromShare({required String shareUrl}) async => null;
  Future<dynamic> crateApiBindFactoryBindOnlineFactoryWSearchMusiclist({required dynamic sources, required String content, required int page, required int limit}) async => null;
  Future<void> crateApiBindFactoryBindSqlFactoryWAddMusics({required String musicsListName, required dynamic musics}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicDefaultSource({required String musicListName}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicInfo({required dynamic musics, required dynamic newInfos}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusiclistInfo({required dynamic old, required dynamic new_}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusicData() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusiclist({required dynamic musicListInfos}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWCreateMusiclist({required dynamic musiclistInfo}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWDelDuplicateMusicsOfMusiclist({required dynamic musiclistNames}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusiclist({required String musicListName, required dynamic ids}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusics() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetAllMusiclists() async => null;
  Future<void> crateApiBindFactoryBindSqlFactoryWGetAllMusics() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicById() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWInitFromPath({required String filepath}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWReadMusicData({required String source}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusiclist({required dynamic newIds}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusics({required String musicListName, required dynamic newIds}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWReplaceMusics({required String musicListName, required dynamic ids, required dynamic musics}) async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWShutdown() async {}
  
  // 缓存方法
  Future<void> crateApiCacheFsUtilRename({required String from, required String to}) async {}
  Future<void> crateApiCacheFsUtilCopyFile({required String from, required String to}) async {}
  Future<void> crateApiCacheFsUtilCopyDirectory({required String src, required String dst}) async {}
  Future<void> crateApiCacheFsUtilRemoveDir({required String dir}) async {}
  Future<void> crateApiCacheFsUtilRemoveFile({required String file}) async {}
  Future<String> crateApiCacheFileCacheGenHash({required String str}) async => '';
  Future<void> crateApiCacheFileCacheCacheFile({required String file}) async {}
  Future<void> crateApiCacheFileCacheUseCacheFile({required String file}) async {}
  Future<void> crateApiCacheFileCacheDeleteCacheFile({required String file}) async {}
  Future<bool> crateApiCacheMusicCacheHasCachePlayinfo({required dynamic musicInfo}) async => false;
  Future<dynamic> crateApiCacheMusicCacheGetCachePlayinfo({required dynamic musicInfo}) async => null;
  Future<void> crateApiCacheMusicCacheCacheMusic({required dynamic musicInfo, required dynamic playinfo}) async {}
  Future<void> crateApiCacheMusicCacheDeleteMusicCache({required dynamic musicInfo}) async {}
  
  // 外部API方法
  Future<void> crateApiTypesExternApiExternApiFetchUpdate({required dynamic that}) async {}
  Future<dynamic> crateApiTypesExternApiExternApiFromPath({required String path}) async => null;
  Future<dynamic> crateApiTypesExternApiExternApiFromUrl({required String url}) async => null;

  // 添加更多缺失的方法
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistById() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistByName() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsByMusiclistId() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWSearchMusics() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWSearchMusiclists() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWUpdateMusiclist() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWUpdateMusic() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsByIds() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistsByIds() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetAllMusiclistsWithMusics() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistsCount() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsCount() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistMusicsCount() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWBackupDatabase() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWRestoreDatabase() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWOptimizeDatabase() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetDatabaseInfo() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWExportMusiclist() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWImportMusiclist() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistsByPage() async {}
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsByPage() async {}
}

// 临时的API实现
class RustLibApiImpl implements RustLibApi {
  @override
  Future<dynamic> crateApiInitInitBackend({required String storeRoot}) async {
    // 临时实现：返回null
    return null;
  }

  @override
  Future<dynamic> crateApiTypesConfigConfigLoad() async {
    // 临时实现：返回null
    return null;
  }

  @override
  Future<void> crateApiTypesConfigConfigSave({required dynamic that}) async {
    // 临时实现：什么都不做
  }
  
  // 其他方法的临时实现
  @override
  Future<dynamic> crateApiBindFactoryBindAggregatorOnlineFactoryWSearchMusicAggregator({required dynamic aggregators}) async => null;

  @override
  Future<dynamic> crateApiBindFactoryBindOnlineFactoryWGetMusiclistFromShare({required String shareUrl}) async => null;

  @override
  Future<dynamic> crateApiBindFactoryBindOnlineFactoryWSearchMusiclist({required dynamic sources, required String content, required int page, required int limit}) async => null;

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWAddMusics({required String musicsListName, required dynamic musics}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicDefaultSource({required String musicListName}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicInfo({required dynamic musics, required dynamic newInfos}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusiclistInfo({required dynamic old, required dynamic new_}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusicData() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusiclist({required dynamic musicListInfos}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWCreateMusiclist({required dynamic musiclistInfo}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWDelDuplicateMusicsOfMusiclist({required dynamic musiclistNames}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusiclist({required String musicListName, required dynamic ids}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusics() async {}

  @override
  Future<dynamic> crateApiBindFactoryBindSqlFactoryWGetAllMusiclists() async => null;

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetAllMusics() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicById() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWInitFromPath({required String filepath}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWReadMusicData({required String source}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusiclist({required dynamic newIds}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusics({required String musicListName, required dynamic newIds}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWReplaceMusics({required String musicListName, required dynamic ids, required dynamic musics}) async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWShutdown() async {}
  
  @override
  Future<void> crateApiCacheFsUtilRename({required String from, required String to}) async {}
  
  @override
  Future<void> crateApiCacheFsUtilCopyFile({required String from, required String to}) async {}
  
  @override
  Future<void> crateApiCacheFsUtilCopyDirectory({required String src, required String dst}) async {}
  
  @override
  Future<void> crateApiCacheFsUtilRemoveDir({required String dir}) async {}
  
  @override
  Future<void> crateApiCacheFsUtilRemoveFile({required String file}) async {}
  
  @override
  Future<String> crateApiCacheFileCacheGenHash({required String str}) async => '';

  @override
  Future<void> crateApiCacheFileCacheCacheFile({required String file}) async {}

  @override
  Future<void> crateApiCacheFileCacheUseCacheFile({required String file}) async {}

  @override
  Future<void> crateApiCacheFileCacheDeleteCacheFile({required String file}) async {}

  @override
  Future<bool> crateApiCacheMusicCacheHasCachePlayinfo({required dynamic musicInfo}) async => false;

  @override
  Future<dynamic> crateApiCacheMusicCacheGetCachePlayinfo({required dynamic musicInfo}) async => null;

  @override
  Future<void> crateApiCacheMusicCacheCacheMusic({required dynamic musicInfo, required dynamic playinfo}) async {}

  @override
  Future<void> crateApiCacheMusicCacheDeleteMusicCache({required dynamic musicInfo}) async {}
  
  @override
  Future<void> crateApiTypesExternApiExternApiFetchUpdate({required dynamic that}) async {}

  @override
  Future<dynamic> crateApiTypesExternApiExternApiFromPath({required String path}) async => null;

  @override
  Future<dynamic> crateApiTypesExternApiExternApiFromUrl({required String url}) async => null;

  // 新添加方法的实现
  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistById() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistByName() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsByMusiclistId() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWSearchMusics() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWSearchMusiclists() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWUpdateMusiclist() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWUpdateMusic() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsByIds() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistsByIds() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetAllMusiclistsWithMusics() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistsCount() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsCount() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistMusicsCount() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWBackupDatabase() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWRestoreDatabase() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWOptimizeDatabase() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetDatabaseInfo() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWExportMusiclist() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWImportMusiclist() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusiclistsByPage() async {}

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWGetMusicsByPage() async {}
}

// 临时的Wire接口
abstract class RustLibWire {}

// 临时的Wire实现
class RustLibWireImpl implements RustLibWire {}

// 临时的平台API实现
class RustLibApiImplPlatform extends RustLibApiImpl {}

{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"any\", \"default\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 16503403049695105087, "path": 1721391751767290928, "deps": [[228475551920078470, "sqlx_macros", false, 16649765802478796118], [996810380461694889, "sqlx_core", false, 1920729004570506220], [11838249260056359578, "sqlx_sqlite", false, 2819561391886520609]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-f0209cb4418e2135\\dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
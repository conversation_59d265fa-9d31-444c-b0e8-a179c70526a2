import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/types/js_source_manager.dart';
import 'package:app_rhyme/pages/js_source_management_page.dart';
import 'package:app_rhyme/utils/global_vars_simplified.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化全局变量
  await initGlobalVars();

  // 初始化JS源管理器
  await JsSourceManager.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoApp(
      title: 'AppRhyme - JS源管理演示',
      theme: const CupertinoThemeData(
        primaryColor: CupertinoColors.systemBlue,
      ),
      home: const HomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    // 监听源管理器变化
    JsSourceManager.addListener(_onSourcesChanged);
  }

  @override
  void dispose() {
    JsSourceManager.removeListener(_onSourcesChanged);
    super.dispose();
  }

  void _onSourcesChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
    final backgroundColor = isDarkMode ? CupertinoColors.black : CupertinoColors.white;
    final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
    
    final activeSource = JsSourceManager.activeSource;
    final sourcesCount = JsSourceManager.sources.length;

    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        middle: Text(
          'AppRhyme - JS源管理',
          style: TextStyle(color: textColor).useSystemChineseFont(),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                'JS音乐源管理系统',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ).useSystemChineseFont(),
              ),
              const SizedBox(height: 8),
              Text(
                '多源管理，自由切换',
                style: TextStyle(
                  fontSize: 16,
                  color: textColor.withOpacity(0.7),
                ).useSystemChineseFont(),
              ),
              const SizedBox(height: 32),
              
              // 状态卡片
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: isDarkMode ? CupertinoColors.systemGrey6.darkColor : CupertinoColors.systemGrey6,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          CupertinoIcons.music_note_list,
                          color: CupertinoColors.systemBlue,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '当前状态',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: textColor,
                          ).useSystemChineseFont(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildStatusRow('已导入源数量', '$sourcesCount 个', textColor),
                    const SizedBox(height: 8),
                    _buildStatusRow(
                      '当前活跃源', 
                      activeSource?.name ?? '无', 
                      textColor,
                      valueColor: activeSource != null ? CupertinoColors.systemGreen : CupertinoColors.systemRed,
                    ),
                    if (activeSource != null) ...[
                      const SizedBox(height: 8),
                      _buildStatusRow('源版本', activeSource.source.version, textColor),
                      const SizedBox(height: 8),
                      _buildStatusRow('源作者', activeSource.source.author, textColor),
                    ],
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // 功能按钮
              Text(
                '功能操作',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: textColor,
                ).useSystemChineseFont(),
              ),
              const SizedBox(height: 16),
              
              // 管理源按钮
              SizedBox(
                width: double.infinity,
                child: CupertinoButton(
                  color: CupertinoColors.systemBlue,
                  onPressed: () {
                    Navigator.of(context).push(
                      CupertinoPageRoute(
                        builder: (context) => const JsSourceManagementPage(),
                      ),
                    );
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(CupertinoIcons.settings, color: CupertinoColors.white),
                      const SizedBox(width: 8),
                      Text(
                        '管理JS音乐源',
                        style: const TextStyle(
                          color: CupertinoColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ).useSystemChineseFont(),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 测试按钮
              SizedBox(
                width: double.infinity,
                child: CupertinoButton(
                  color: CupertinoColors.systemGreen,
                  onPressed: activeSource != null ? _testActiveSource : null,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(CupertinoIcons.play_circle, color: CupertinoColors.white),
                      const SizedBox(width: 8),
                      Text(
                        '测试当前源',
                        style: const TextStyle(
                          color: CupertinoColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ).useSystemChineseFont(),
                      ),
                    ],
                  ),
                ),
              ),
              
              const Spacer(),
              
              // 底部信息
              Center(
                child: Column(
                  children: [
                    Text(
                      'AppRhyme v1.0.9',
                      style: TextStyle(
                        color: textColor.withOpacity(0.5),
                        fontSize: 14,
                      ).useSystemChineseFont(),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'JS音乐源多源管理系统演示',
                      style: TextStyle(
                        color: textColor.withOpacity(0.5),
                        fontSize: 12,
                      ).useSystemChineseFont(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color textColor, {Color? valueColor}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: textColor.withOpacity(0.8),
            fontSize: 14,
          ).useSystemChineseFont(),
        ),
        Text(
          value,
          style: TextStyle(
            color: valueColor ?? textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ).useSystemChineseFont(),
        ),
      ],
    );
  }

  void _testActiveSource() {
    final activeSource = JsSourceManager.activeSource;
    if (activeSource == null) return;

    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('测试源连接', style: const TextStyle().useSystemChineseFont()),
        content: Text(
          '正在测试源: ${activeSource.name}\n\n这是一个演示版本，实际的源测试功能需要完整的后端支持。',
          style: const TextStyle().useSystemChineseFont(),
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('确定', style: const TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}

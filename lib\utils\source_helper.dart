import 'package:app_rhyme/utils/const_vars.dart';

String? nextSource(List<String> availableSources) {
  // 传入已经使用过的源，返回下一个可用的源，如果没有下一个源了，返回 null
  // 只使用网易云音乐源，不再支持酷我音乐源

  // 如果网易云音乐源已经使用过，则没有其他源可用
  if (availableSources.contains(sourceWangYi)) {
    return null;
  } else {
    // 如果网易云音乐源未使用过，返回网易云音乐源
    return sourceWangYi;
  }
}

String sourceToShort(String source) {
  switch (source) {
    case sourceWangYi:
      return 'wy';
    default:
      return source;
  }
}
